#ifndef __MY_UART_H
#define __MY_UART_H

#include "my_define.h"

#define MAIXCAM_LUNKUO_POINTS 40 // 最大轮廓点数量
#define MAIXCAM_LASER_POINTS  1  // 最大红色激光笔点数量

typedef enum {
  RETURN_LUNKUO_POINTS = 0, // 返回轮廓点
  RETURN_RED_LASER_POINT,   // 返回红色激光笔点
  TRIGER_CALIBRATION,         // 触发视觉校准
} Uart_Event_t;

void my_printf(UART_HandleTypeDef* huart, const char* format, ...);
void Uart_Proc(void);
void Uart_Get(UART_HandleTypeDef *huart, uint8_t *data);
void MaixCam_Prase(void);
void Uart_Trig_To_Maixcam(Uart_Event_t event);

#endif