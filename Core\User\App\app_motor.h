#ifndef __APP_MOTOR_H_
#define __APP_MOTOR_H_

#include "my_define.h"

/* 电机控制宏定义 */
#define MOTOR_X_ADDR        0x01          // X轴电机地址
#define MOTOR_Y_ADDR        0x02         // Y轴电机地址
#define MOTOR_UART        huart3        // 电机串口 
// #define MOTOR_Y_UART        huart5        // Y轴电机串口 右
#define MOTOR_MAX_SPEED     30.f            // 电机最大转速(RPM)
#define MOTOR_ACCEL         0             // 电机加速度(0表示直接启动)
#define MOTOR_SYNC_FLAG     true         // 电机同步标志
#define MOTOR_MAX_ANGLE     40            // 电机最大角度限制(±40°)

#define MOTOR_STEP_ANGLE     0.1125f        // 电机步进角度(单位：度)
#define MOTOR_STEP_calibrating  5 // 步进角度对应的脉冲数(20倍步进角度)

/* 步进电机结构体 */
typedef struct {
  /* 当前坐标 */
  int32_t current_position_x;
  int32_t current_position_y;

  /* 校准数据 */
  int32_t up_loction;
  int32_t down_loction;
  int32_t left_loction;
  int32_t right_loction;

  /* 中心归位点坐标*/
  int32_t central_x;
  int32_t central_y;
} ZDT_Yuntai_t;

/* 函数声明 */
void Motor_Init(void);                    // 电机初始化
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent);  // 设置XY电机速度(百分比)
void Motor_Stop(void);                    // 停止所有电机

/* 测试函数 */
void Motor_Calibrations_Position(void);
/* 设置当前位置为零点 */
void Motor_Save_Zero_Position(void);
/* 回到零点 */
void Motor_Go_Zero_Position(void);
/* 移动步数 */
void Motor_StepMove(int32_t step_x, int32_t step_y);
#endif /* __APP_MOTOR_H_ */
