#include "my_define.h"

#define DEBUG

void my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
  char buffer[256]; // Buffer for formatted string
  va_list args;     // Variable argument list

  // Initialize the variable argument list
  va_start(args, format);

  // Format the string into the buffer
  vsnprintf(buffer, sizeof(buffer), format, args);

  // Transmit the formatted string over UART
  HAL_UART_Transmit(huart, (uint8_t *)buffer, strlen(buffer), HAL_MAX_DELAY);

  // Clean up the variable argument list
  va_end(args);
}

/* 串口接收缓冲区 */
#define UART1_RX_BUFFER_SIZE 100
uint8_t uart1_rx_buffer[UART1_RX_BUFFER_SIZE];
uint8_t uart1_rx_index = 0;
uint8_t
  uart5_rx_buffer[256]; // 视觉串口接收缓冲区 256个字节，可以存储40个点的坐标
uint8_t uart5_rx_index = 0;

/* 串口处理数组 */
uint8_t maixcam_data[256]; // 视觉串口接收数据处理数组
// uint8_t uart1_rx_flag = 0; // 标志位，表示是否接收到数据
/* 中断服务函数 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
  /* 电脑串口 */
  if (huart->Instance == USART1) {
    uart1_rx_index++;
    if (uart1_rx_index >= UART1_RX_BUFFER_SIZE) {
      uart1_rx_index = 0; // Reset index if buffer is full
    }

    /* 重新开启中断 */
    HAL_UART_Receive_IT(huart, &uart1_rx_buffer[uart1_rx_index], 1);
  }
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
  /* 视觉串口 */
  if (huart == &huart5) {
    /* 将串口数据转移到处理数组 */
    memcpy(maixcam_data, uart5_rx_buffer, Size);
    /* 清空串口数组 */
    memset(uart5_rx_buffer, 0, sizeof(uart5_rx_buffer));
/* 打印验证 */
#ifdef DEBUG
    my_printf(&huart1, "%s", maixcam_data);
#endif
    /* 处理数据 */
    MaixCam_Prase(); // 解析视觉数据
    /* 重新开启串口空闲中断 */
    HAL_UARTEx_ReceiveToIdle_DMA(&huart5, uart5_rx_buffer,
                                 256); // 启动视觉串口接收DMA
    __HAL_DMA_DISABLE_IT(huart5.hdmarx, DMA_IT_HT);
  }
}
/* 视觉处理函数
1. 三种信息 ：
  1.1 轮廓点坐标：帧头 为 0xAA 帧尾 为 '!'
  1.2 红色激光笔坐标：帧头 为 0xBB 为 帧尾 为'@'
*/

uint32_t lunkuo_point[MAIXCAM_LUNKUO_POINTS][2];
uint32_t laser_point[MAIXCAM_LASER_POINTS][2];
extern volatile uint8_t is_get_loction;
extern volatile uint8_t is_get_lunkuo; // 是否获取轮廓点

void MaixCam_Prase(void)
{
  uint8_t state = 0; // 1-轮廓点坐标 2-红色激光笔坐标
  // x的个十百位数组
  uint8_t x[3] = {0}; // 0-百，1-十，2-个
  // y的个十百位数组
  uint8_t y[3] = {0};

  /* 检验帧头帧尾 */
  if (maixcam_data[0] == '#' &&
      maixcam_data[MAIXCAM_LUNKUO_POINTS * 6 + 1] == '!') {
    state = 1; // 轮廓点坐标
  } else if (maixcam_data[0] == '$' &&
             maixcam_data[MAIXCAM_LASER_POINTS * 6 + 1] == '!') {
    state = 2; // 红色激光笔坐标
  } else {
    return; // 无效数据
  }

  switch (state) {
    case 1:
      /* 处理轮廓点坐标数据 */
      /* 一共四十个点 */
      for (int i = 0; i < MAIXCAM_LUNKUO_POINTS; i++) {
        // 每个点有x，y 坐标，由个-十-百组成
        for (int j = 0; j < 3; j++) {
          x[j] = maixcam_data[i * 6 + j + 1] - '0'; // 获取x坐标
        }
        for (int j = 0; j < 3; j++) {
          y[j] = maixcam_data[i * 6 + j + 4] - '0'; // 获取y坐标
        }
        /* 将x，y坐标转换为十进制 */
        lunkuo_point[i][0] = x[0] * 100 + x[1] * 10 + x[2]; // x坐标
        lunkuo_point[i][1] = y[0] * 100 + y[1] * 10 + y[2]; // y坐标

        is_get_lunkuo = 1; // 设置获取轮廓点标志位
      }
      break;
    case 2:
      /* 处理红色激光笔坐标数据 */
      /* 只有一个点 */
      for (int j = 0; j < 3; j++) {
        x[j] = maixcam_data[1 + j] - '0'; // 获取x坐标
      }
      for (int j = 0; j < 3; j++) {
        y[j] = maixcam_data[4 + j] - '0'; // 获取y坐标
      }
      /* 将x，y坐标转换为十进制 */
      laser_point[0][0] = x[0] * 100 + x[1] * 10 + x[2]; // x坐标
      laser_point[0][1] = y[0] * 100 + y[1] * 10 + y[2]; // y坐标

      is_get_loction = 1; // 设置定位标志位
      break;
  }

  /* 清空 */
  memset(maixcam_data, 0, sizeof(maixcam_data));
}

extern ZDT_Yuntai_t yuntai; // Assuming yuntai is defined globally

void Uart_Trig_To_Maixcam(Uart_Event_t event)
{
  switch (event) {
    case RETURN_LUNKUO_POINTS:
      HAL_UART_Transmit(&huart5, (uint8_t *)"f", 1,
                        0xFFFF); // 触发视觉获取轮廓点
      break;

    case RETURN_RED_LASER_POINT:
      HAL_UART_Transmit(&huart5, (uint8_t *)"g", 1,
                        0xFFFF); // 触发视觉获取轮廓点
      break;
    case TRIGER_CALIBRATION:
      HAL_UART_Transmit(&huart5, (uint8_t *)"h", 1,
                        0xFFFF); // 触发视觉校准
  }
}

void Uart_Proc(void)
{
  static uint8_t go_line_state = 0;
  void Go_Line(int32_t current_x, int32_t current_y, int32_t target_x,
               int32_t target_y, int32_t num);
// my_printf(&huart1, "Uart_Proc Start!\n");
#if 0
  if(uart1_rx_index > 0) {
    // Process received data
    for (uint8_t i = 0; i < uart1_rx_index; i++) {
      // Example: Print received data
      my_printf(&huart1, "Received: %c\n", uart1_rx_buffer[i]);
    }
    uart1_rx_index = 0; // Reset index after processing
    HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
  }
#endif
  if (uart1_rx_index > 0) {
    uart1_rx_index    = 0;               // 清空接收缓冲区
    huart1.pRxBuffPtr = uart1_rx_buffer; // 设置接收缓冲区指针

    if (uart1_rx_buffer[0] == 'm') {
      // 移动到目标位置
      Motor_Go_Zero_Position(); // 回到零点
    } else if (uart1_rx_buffer[0] == 'C') {
      Motor_Calibrations_Position();
    } else if (uart1_rx_buffer[0] == 's') {
      // 保留零点
      Motor_Save_Zero_Position();
    }
    /* 触发相机传送轮廓 */
    else if (uart1_rx_buffer[0] == 'f') {
      Uart_Trig_To_Maixcam(RETURN_LUNKUO_POINTS);
    } else if (uart1_rx_buffer[0] == 'l') {
      // 打印轮廓点
      for (int i = 0; i < MAIXCAM_LUNKUO_POINTS; i++) {
        my_printf(&huart1, "Lunkuo Point %d: (%d, %d)\n", i, lunkuo_point[i][0],
                  lunkuo_point[i][1]);
      }
    } else if (uart1_rx_buffer[0] == 'z') // 视觉返回数据处理
    {
      Uart_Trig_To_Maixcam(RETURN_RED_LASER_POINT);
    } else if (uart1_rx_buffer[0] == 'g') {
#ifdef DEBUG
      my_printf(&huart1, "Triggering red laser point retrieval\n");
#endif
      Go_Line(0, 0, yuntai.left_loction, yuntai.up_loction, 10);
    } else if (uart1_rx_buffer[0] == 'h') {
      Go_Line(yuntai.left_loction, yuntai.up_loction, yuntai.right_loction,
              yuntai.up_loction, 10);
    } else if (uart1_rx_buffer[0] == 'b') {
      // 测试电机分解速度
      Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_X_ADDR, 0, 15, MOTOR_ACCEL, 1, true,
                         false);
      HAL_Delay(1);
      Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_Y_ADDR, 0, 10, MOTOR_ACCEL, 1, true,
                         false);
      // 延时，避免过快执行
      HAL_Delay(1);
      // 执行同步
      // Emm_V5_Synchronous_motion(&MOTOR_UART, 0);
    }
  }
}
