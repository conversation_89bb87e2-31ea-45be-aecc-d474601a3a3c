#include "my_define.h"

#define DEBUG

ZDT_Yuntai_t yuntai;

/**
 * @brief 电机初始化函数
 */
void Motor_Init(void)
{
  /* 使能X轴电机 */
  Emm_V5_En_Control(&MOTOR_UART, MOTOR_X_ADDR, true, 0);

  /* 使能Y轴电机 */
  Emm_V5_En_Control(&MOTOR_UART, MOTOR_Y_ADDR, true, 0);

  /* 初始停止 */
  // Motor_Stop();
  /* 回归零点 */
  Motor_Go_Zero_Position();
  /* 校准值修改 */
  yuntai.up_loction    = 114;
  yuntai.down_loction  = -104;
  yuntai.left_loction  = 138;
  yuntai.right_loction = -113;
}

/**
 * @brief 设置XY轴电机速度
 * @param x_percent X轴速度百分比，范围-100到100
 * @param y_percent Y轴速度百分比，范围-100到100
 */
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
  uint8_t x_dir, y_dir;
  uint16_t x_speed, y_speed;

  /* 限制百分比范围 */
  if (x_percent > 100) x_percent = 100;
  if (x_percent < -100) x_percent = -100;
  if (y_percent > 100) y_percent = 100;
  if (y_percent < -100) y_percent = -100;

  /* 设置X轴方向 */
  if (x_percent >= 0) {
    x_dir = 0; /* CW方向 */
  } else {
    x_dir     = 1;          /* CCW方向 */
    x_percent = -x_percent; /* 取绝对值 */
  }

  /* 设置Y轴方向 */
  if (y_percent >= 0) {
    y_dir = 0; /* CW方向 */
  } else {
    y_dir     = 1;          /* CCW方向 */
    y_percent = -y_percent; /* 取绝对值 */
  }

  /* 计算实际速度值(百分比转换为RPM) */
  x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
  y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

  /* 控制X轴电机 */
  Emm_V5_Vel_Control(&MOTOR_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL,
                     MOTOR_SYNC_FLAG);

  /* 控制Y轴电机 */
  Emm_V5_Vel_Control(&MOTOR_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL,
                     MOTOR_SYNC_FLAG);
}

/**
 * @brief 停止所有电机
 */
void Motor_Stop(void)
{
  /* 停止X轴电机 */
  Emm_V5_Stop_Now(&MOTOR_UART, 0, 0);
  HAL_Delay(10); // 等待停止完成
}

/* 保留当前位置为零点 */
void Motor_Save_Zero_Position(void)
{
  /* X轴电机 */
  Emm_V5_Origin_Set_O(&MOTOR_UART, 0, 1);

  // /* Y轴电机 */
  // Emm_V5_Origin_Set_O(&MOTOR_UART, 1, 1);
}

/* 回到零点 */
void Motor_Go_Zero_Position(void)
{
  /* X轴电机 */
  Emm_V5_Origin_Trigger_Return(&MOTOR_UART, 0, 0, 0);

  HAL_Delay(10);
}

static int32_t Change_to_Step(int32_t pulse)
{
  /* 将脉冲转换为步进角度 */
  return (pulse * 360.f / 65535 / 0.1125f); // 步进角度为0.1125度
}

/* 获取电机位置（脉冲）*/
int32_t Motor_Get_Position(UART_HandleTypeDef *huart, int addr)
{
  /* 定义变量 */
  uint8_t rec[10] = {0};
  uint8_t index   = 0;
  /* 发送指令 */
  Emm_V5_Read_Sys_Params(huart, addr, S_CPOS);
  // HAL_Delay(100); // 等待数据返回
  /* 接收指令（阻塞） */
  for (index = 0; index < 10; index++) {
    HAL_UART_Receive(huart, &rec[index], 1, 0xFFFFF);
    /* 接收到校验位 */
    if (rec[index] == 0x6b) break;
  }
  // 由于实际有效数据位为后六位
  for (int j = 0; j < 6; j++) { rec[j] = rec[j + index - 5]; }
  if (rec[5] == 0x6b) {
    int32_t location = (int32_t)(((int32_t)rec[1] << 24) |

                                 ((int32_t)rec[2] << 16) |

                                 ((int32_t)rec[3] << 8) |

                                 ((int32_t)rec[4] << 0)

    );
    // 说明为负数
    if (rec[0] != 0) {
      location = -location; // 如果rec[0]不为0，表示方向为逆时针
    }
#ifdef DEBUG
    my_printf(&huart1, "Motor Position: %d\n", Change_to_Step(location));
#endif
    return Change_to_Step(location); // 返回步进值
  } else {
    // return 65535;
  }
}
/**
 * @brief 校准电机位置
 * @note
 * 通过串口发送WSAD向上、向下、向左、向右和回车键进行控制，步进角度为0.1125 *
 * 20
 *
 */
void Motor_Calibrations_Position(void)
{
  uint8_t is_calibrating_upLeft = 1; // 是否正在校准左上角
  uint8_t if_calibrating_done   = 0; // 是否结束校准
  // uint8_t uart_rx_data          = 0; // 串口接收数据缓冲区 #局部变量
  /* 前往左上端点 */
  Motor_StepMove(yuntai.left_loction, yuntai.up_loction);

  /* 进入一个状态机 */
  while (1) {
    // HAL_UART_Receive(&huart1, &uart_rx_data, 1, 0xFFFFF);
    if (uart1_rx_index > 0) {
      uart1_rx_index    = 0;               // 清空接收缓冲区
      huart1.pRxBuffPtr = uart1_rx_buffer; // 设置接收缓冲区指针

      switch (uart1_rx_buffer[0]) {
        /* 向上 */
        case 'W':
          Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_Y_ADDR, 0, MOTOR_MAX_SPEED, 0,
                             MOTOR_STEP_calibrating, 0,
                             false); // 位置模式
          break;
        /* 向下 */
        case 'S':
          Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_Y_ADDR, 1, MOTOR_MAX_SPEED, 0,
                             MOTOR_STEP_calibrating, 0,
                             false); // 位置模式
          break;
        /* 向左 */
        case 'A':
          Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_X_ADDR, 0, MOTOR_MAX_SPEED, 0,
                             MOTOR_STEP_calibrating, 0,
                             false); // 位置模式
          break;
        /* 向右 */
        case 'D':
          Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_X_ADDR, 1, MOTOR_MAX_SPEED, 0,
                             MOTOR_STEP_calibrating, 0,
                             false); // 位置模式
          break;
        /* 校准结束 */
        case 0x0A:
          if (is_calibrating_upLeft == 1) {
            is_calibrating_upLeft = 0; // 结束校准左上角
            /* 触发视觉校准 */
            Uart_Trig_To_Maixcam(TRIGER_CALIBRATION); // 触发视觉校准
            /* 保留位置 */
            HAL_Delay(100);
            yuntai.up_loction   = Motor_Get_Position(&MOTOR_UART, MOTOR_Y_ADDR);
            yuntai.left_loction = Motor_Get_Position(&MOTOR_UART, MOTOR_X_ADDR);
            HAL_Delay(100);
            /* 前往右下端点 */
            Motor_StepMove(yuntai.right_loction, yuntai.down_loction);
          } else if (is_calibrating_upLeft == 0) {
            if_calibrating_done = 1; // 结束校准
            Uart_Trig_To_Maixcam(TRIGER_CALIBRATION); // 触发视觉校准
            HAL_Delay(100);
            /* 保留位置 */
            yuntai.down_loction = Motor_Get_Position(&MOTOR_UART, MOTOR_Y_ADDR);
            yuntai.right_loction =
              Motor_Get_Position(&MOTOR_UART, MOTOR_X_ADDR);
            HAL_Delay(100);
          }
          break;
        default:
          break;
      }
    }

    // 退出校准程序 并计算中心点坐标
    if (if_calibrating_done == 1) {
      yuntai.central_x = (yuntai.right_loction + yuntai.left_loction) / 2;
      yuntai.central_y = (yuntai.up_loction + yuntai.down_loction) / 2;

      /* 前往中心点 */
      Motor_StepMove(yuntai.central_x, yuntai.central_y);
/* 打印中心点坐标 */
#ifdef DEBUG
      my_printf(&huart1, "Center X: %d, Y: %d\n", yuntai.central_x,
                yuntai.central_y);
#endif
      break; // 退出校准循环
    }
  }
}

float my_abs(float a)
{
  if (a < 0)
    return -a;
  else
    return a;
}

/**
 * @brief 移动
 * @param target_x 目标X坐标
 * @param target_y 目标Y坐标
 */
void Motor_StepMove(int32_t step_x, int32_t step_y)
{
  /* 前往 距离点 */
  Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_X_ADDR, (step_x >= 0) ? 0 : 1, 15,
                     MOTOR_ACCEL, (uint16_t)my_abs(step_x), true, true);
  HAL_Delay(1);
  Emm_V5_Pos_Control(&MOTOR_UART, MOTOR_Y_ADDR, (step_y >= 0) ? 0 : 1, 15,
                     MOTOR_ACCEL, (uint16_t)my_abs(step_y), true, true);
  HAL_Delay(1);
  Emm_V5_Synchronous_motion(&MOTOR_UART, 0);
}
